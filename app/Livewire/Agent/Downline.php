<?php

namespace App\Livewire\Agent;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

#[Layout('layouts.account.app')]
#[Title('Downline')]
class Downline extends Component
{
    public $search = '';
    public $downlineData = [];
    public $totalReferrals = 0;
    public $previousMonthReferrals = 0;
    public $currentMonthReferrals = 0;

    public function mount()
    {
        $this->loadStatistics();
    }

    public function updatedSearch()
    {
        $this->loadDownlineData();
    }

    public function clearSearch()
    {
        $this->search = '';
        $this->loadDownlineData();
    }

    public function loadStatistics()
    {
        $userId = Auth::user()->id;

        // Total referrals
        $this->totalReferrals = DB::table('senarai_pelanggan')
            ->where('acs_users_id', $userId)
            ->count();

        // Previous month referrals
        $previousMonth = Carbon::now()->subMonth();
        $this->previousMonthReferrals = DB::table('senarai_pelanggan')
            ->where('acs_users_id', $userId)
            ->whereYear('tarikh', $previousMonth->year)
            ->whereMonth('tarikh', $previousMonth->month)
            ->count();

        // Current month referrals
        $currentMonth = Carbon::now();
        $this->currentMonthReferrals = DB::table('senarai_pelanggan')
            ->where('acs_users_id', $userId)
            ->whereYear('tarikh', $currentMonth->year)
            ->whereMonth('tarikh', $currentMonth->month)
            ->count();
    }

    public function loadDownlineData()
    {
        $query = DB::table('senarai_pelanggan')
            ->select('senarai_pelanggan.nama', 'senarai_pelanggan.email', 'senarai_pelanggan.tarikh')
            ->where('senarai_pelanggan.acs_users_id', Auth::user()->id);

        // Apply search filter if search term exists
        if (!empty($this->search)) {
            $query->where(function ($q) {
                $q->where('senarai_pelanggan.nama', 'like', '%' . $this->search . '%')
                  ->orWhere('senarai_pelanggan.email', 'like', '%' . $this->search . '%');
            });
        }

        $this->downlineData = $query->orderBy('senarai_pelanggan.tarikh', 'desc')->get();
    }

    public function render()
    {
        $this->loadDownlineData();

        return view('livewire.agent.downline', [
            'downlineData' => $this->downlineData,
        ]);
    }
}
