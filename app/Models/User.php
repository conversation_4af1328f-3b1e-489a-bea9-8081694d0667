<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use App\Traits\GeneratesReferralCode;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes, HasUlids, GeneratesReferralCode;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'acs_users';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'acs_role_id',
        'username',
        'email',
        'phone',
        'password',
        'status',
        'activated_at',
        'activation_email_sent_at',
        'acs_coorperative_id',
        'acs_coorperative_branch_id',
        'invited_by',
        'referral_code',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'password' => 'hashed',
            'activated_at' => 'datetime',
            'activation_email_sent_at' => 'datetime',
        ];
    }

    /**
     * Status constants
     */
    const STATUS_PENDING = 'pending';
    const STATUS_ACTIVE = 'active';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_INVITATION_SENT = 'invitation_sent';

    /**
     * Check if user is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if user is pending activation
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if user is suspended
     */
    public function isSuspended(): bool
    {
        return $this->status === self::STATUS_SUSPENDED;
    }

    /**
     * Check if user needs to complete registration
     */
    public function needsActivation(): bool
    {
        return $this->isPending() && (!$this->password || !$this->phone);
    }

    /**
     * Activate the user account
     */
    public function activate(): void
    {
        $this->update([
            'status' => self::STATUS_ACTIVE,
            'activated_at' => now(),
        ]);
    }

    /**
     * Check if activation email can be resent (10-minute cooldown)
     */
    public function canResendActivationEmail(): bool
    {
        if (!$this->activation_email_sent_at) {
            return true;
        }

        return $this->activation_email_sent_at->addMinutes(10)->isPast();
    }

    /**
     * Get the remaining time until resend is allowed
     */
    public function getResendCooldownMinutes(): int
    {
        if (!$this->activation_email_sent_at || $this->canResendActivationEmail()) {
            return 0;
        }

        return $this->activation_email_sent_at->addMinutes(10)->diffInMinutes(now());
    }

    /**
     * Update the activation email sent timestamp
     */
    public function markActivationEmailSent(): void
    {
        $this->update([
            'activation_email_sent_at' => now(),
        ]);
    }

    /**
     * Get the role that owns the user.
     */
    public function role()
    {
        return $this->belongsTo(AcsRole::class, 'acs_role_id');
    }

    /**
     * Get the cooperative that the user belongs to.
     */
    public function cooperative()
    {
        return $this->belongsTo(AcsCooperative::class, 'acs_coorperative_id');
    }

    /**
     * Get the cooperative branch that the user belongs to.
     */
    public function cooperativeBranch()
    {
        return $this->belongsTo(AcsCooperativeBranch::class, 'acs_coorperative_branch_id');
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(string $roleName): bool
    {
        return $this->role && $this->role->name === $roleName;
    }

    /**
     * Check if user has any of the given roles
     *
     * @param array<string> $roles
     * @return bool
     */
    public function hasAnyRole(array $roles): bool
    {
        return $this->role && in_array($this->role->name, $roles);
    }

    /**
     * Get notifications sent to this user
     */
    public function receivedNotifications()
    {
        return $this->hasMany(AcsNotification::class, 'recipient_id', 'id');
    }

    /**
     * Get notifications sent by this user
     */
    public function sentNotifications()
    {
        return $this->hasMany(AcsNotification::class, 'sender_id', 'id');
    }

    /**
     * Get the role name
     */
    public function getRoleName(): ?string
    {
        return $this->role?->name;
    }

    /**
     * Get the user details for this user.
     */
    public function userDetails()
    {
        return $this->hasOne(AcsUsersDetail::class, 'acs_users_id', 'id');
    }

    /**
     * Get the status history for this user.
     */
    public function statusHistory()
    {
        return $this->hasMany(AcsUsersStatusHistory::class, 'acs_users_id', 'id');
    }

    /**
     * Get the latest status history for this user.
     */
    public function latestStatusHistory()
    {
        return $this->hasOne(AcsUsersStatusHistory::class, 'acs_users_id', 'id')->latest();
    }

    /**
     * Get the user who invited this user.
     */
    public function inviter()
    {
        return $this->belongsTo(User::class, 'invited_by');
    }

    /**
     * Get the bank details for this user.
     */
    public function bankDetails()
    {
        return $this->hasMany(AcsBankDetail::class, 'acs_users_id', 'id');
    }

    /**
     * Get the branch change logs for this user.
     */
    public function branchChangeLogs()
    {
        return $this->hasMany(AcsUsersBranchChangeLog::class, 'acs_users_id', 'id');
    }

    /**
     * Get the latest branch change log for this user.
     */
    public function latestBranchChangeLog()
    {
        return $this->hasOne(AcsUsersBranchChangeLog::class, 'acs_users_id', 'id')->latest();
    }

    /**
     * Get permissions set by this main agent for sub-main-agents.
     */
    public function setPermissionsFor()
    {
        return $this->hasMany(AcsUserPermission::class, 'main_agent_id', 'id');
    }

    /**
     * Get permissions set for this sub-main-agent.
     */
    public function receivedPermissions()
    {
        return $this->hasOne(AcsUserPermission::class, 'sub_main_agent_id', 'id');
    }

    /**
     * Check if user has permission for a resource and action (for sub-main-agents).
     */
    public function hasCustomPermission(string $resource, string $action = 'view'): bool
    {
        // If user is not a sub-main-agent, they have full access
        if (!$this->hasRole('sub-main-agent')) {
            return true;
        }

        // Check if user has received permissions record
        if (!$this->receivedPermissions || !$this->receivedPermissions->is_active) {
            return false;
        }

        return $this->receivedPermissions->hasPermission($resource, $action);
    }

    /**
     * Check if a menu item should be visible for this user.
     */
    public function isMenuVisible(string $menuKey): bool
    {
        // If user is not a sub-main-agent, show all menus
        if (!$this->hasRole('sub-main-agent')) {
            return true;
        }

        // Check if user has received permissions record
        if (!$this->receivedPermissions || !$this->receivedPermissions->is_active) {
            return false;
        }

        // Check if menu is hidden
        return !$this->receivedPermissions->isMenuHidden($menuKey);
    }

    /**
     * Get the appropriate dashboard URL based on user role and permissions.
     */
    public function getDashboardUrl(): string
    {
        if ($this->hasRole('admin')) {
            return url('admin/dashboard');
        } elseif ($this->hasRole('main-agent') || $this->hasRole('master-agent')) {
            return url('main-agent/dashboard');
        } elseif ($this->hasRole('sub-main-agent')) {
            // Check if sub-main-agent has dashboard permission
            if ($this->hasCustomPermission('dashboard')) {
                return url('main-agent/dashboard');
            }
            // Fallback to profile if no dashboard access
            return url('main-agent/profile');
        } else {
            return url('agent/dashboard');
        }
    }

    /**
     * Check if user can access a specific route.
     */
    public function canAccessRoute(string $route, string $action = 'view'): bool
    {
        // Map routes to resources
        $routePermissions = [
            'main-agent/dashboard' => 'dashboard',
            'main-agent/profile' => 'profile',
            'main-agent/branch-list' => 'branch',
            'main-agent/agent' => 'agents',
            'main-agent/bank-account' => 'bank_account',
            'main-agent/change-password' => 'password_management',
            'main-agent/aggrement-list' => 'agreements',
            'main-agent/payment-settings' => 'settings',
        ];

        $resource = $routePermissions[$route] ?? null;

        if (!$resource) {
            return true; // Allow access to unmapped routes
        }

        return $this->hasCustomPermission($resource, $action);
    }
}
