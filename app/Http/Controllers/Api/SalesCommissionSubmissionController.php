<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;

class SalesCommissionSubmissionController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        Log::info('commission_requests', $request->all());
        $requestSignature = hash('sha256', implode('|', $request->all()));
        //calculate signature
        return response()->json([
            'error' => false,
            'status' => 201,
            'data' => [
                'signature' => $requestSignature
            ]
        ]);
    }
}
